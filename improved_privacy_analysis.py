#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版隐私保护行为分析 - 使用有序Probit模型精确复现R结果
Improved Privacy Analysis - Using Ordered Probit to Match R Results Exactly
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from statsmodels.miscmodels.ordinal_model import OrderedModel
import warnings
warnings.filterwarnings('ignore')

class ImprovedPrivacyAnalysis:
    def __init__(self, data_path):
        """初始化分析类"""
        self.data_path = data_path
        self.data = None
        self.final_data = None
        self.results = {}

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.data = pd.read_excel(self.data_path)
        print(f"数据形状: {self.data.shape}")
        return self.data

    def create_network_addiction_score(self):
        """创建网络成瘾得分 - 精确复现R代码"""
        # R代码: temp3<-apply(data[,c(75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91)], 1, sum)
        r_cols = [75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91]
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        # 计算网络成瘾总分
        addiction_score = self.data.iloc[:, python_cols].sum(axis=1)

        print(f"网络成瘾得分统计:")
        print(f"均值: {addiction_score.mean():.2f}")
        print(f"标准差: {addiction_score.std():.1f}")
        print(f"最小值: {addiction_score.min()}")
        print(f"最大值: {addiction_score.max()}")

        return addiction_score

    def create_privacy_perception_score(self):
        """创建隐私感知得分 - 精确复现R代码"""
        # R代码: pv<-apply(data[,c(147,149,150)], 1, mean)
        r_cols = [147, 149, 150]
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        # 计算隐私感知均值
        privacy_score = self.data.iloc[:, python_cols].mean(axis=1)

        print(f"隐私感知得分统计:")
        print(f"均值: {privacy_score.mean():.2f}")
        print(f"标准差: {privacy_score.std():.2f}")
        print(f"最小值: {privacy_score.min()}")
        print(f"最大值: {privacy_score.max()}")

        return privacy_score

    def create_control_variables(self):
        """创建控制变量 - 精确复现R代码"""
        # R代码: geography<-data[,c(7:14,43:45)]
        r_cols = list(range(7, 15)) + list(range(43, 46))  # R的7:14和43:45
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        control_vars = self.data.iloc[:, python_cols].copy()

        # R代码中的列名
        control_names = ['gender','location','university','school','major',
                        'edu_background','grade','hukou','edu_fa','edu_ma','income']

        control_vars.columns = control_names

        return control_vars

    def create_dependent_variables(self):
        """创建因变量 - 精确复现R代码"""
        # R代码: data[,c(161:164)] -> reject, platform, parent, law
        r_cols = list(range(161, 165))  # R的161:164
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        dep_vars = self.data.iloc[:, python_cols].copy()

        # R代码中的列名 - 注意顺序：reject, platform, parent, law
        dep_names = ['reject', 'platform', 'parent', 'law']
        dep_vars.columns = dep_names

        return dep_vars

    def prepare_final_dataset(self):
        """准备最终分析数据集"""
        print("正在准备分析数据集...")

        # 创建各种变量
        addiction_score = self.create_network_addiction_score()
        privacy_score = self.create_privacy_perception_score()
        control_vars = self.create_control_variables()
        dep_vars = self.create_dependent_variables()

        # R代码: finaldta<-cbind(geography,temp3,pv,data[,c(161:164)])
        self.final_data = pd.concat([
            control_vars,
            pd.Series(addiction_score, name='temp3'),
            pd.Series(privacy_score, name='pv'),
            dep_vars
        ], axis=1)

        # 创建有序因子变量 - 精确复现R代码
        # R代码: y1<-factor(finaldta[,'reject'],levels =1:5, labels = c('low','medium','high','very high','most high'))
        self.final_data['y1'] = self.final_data['reject']  # 拒绝使用
        self.final_data['y2'] = self.final_data['parent']   # 亲友求助
        self.final_data['y3'] = self.final_data['platform'] # 平台反馈
        self.final_data['y4'] = self.final_data['law']      # 诉诸法律

        # 删除缺失值
        initial_rows = len(self.final_data)
        self.final_data = self.final_data.dropna()
        final_rows = len(self.final_data)

        print(f"数据清理: {initial_rows} -> {final_rows} 行 (删除了{initial_rows-final_rows}行缺失值)")

        return self.final_data

    def run_ordered_probit_regression(self, dep_var, include_interaction=True):
        """运行有序Probit回归 - 精确复现R的polr函数"""
        print(f"\n正在运行{dep_var}的有序Probit回归...")

        try:
            # 准备因变量
            y = self.final_data[dep_var].astype(int)

            # 准备自变量
            X = pd.DataFrame()
            X['temp3'] = self.final_data['temp3']
            X['pv'] = self.final_data['pv']

            if include_interaction:
                X['temp3_pv'] = self.final_data['temp3'] * self.final_data['pv']

            # 添加控制变量 - 精确复现R代码中的控制变量
            control_vars = ['gender', 'major', 'grade', 'location', 'hukou', 'edu_fa', 'edu_ma', 'income']
            for var in control_vars:
                if var in self.final_data.columns:
                    # 转换为分类变量
                    X[f'{var}_cat'] = self.final_data[var].astype('category')

            # 使用OrderedModel进行有序Probit回归
            model = OrderedModel(y, X, distr='probit')
            result = model.fit(method='bfgs', maxiter=1000, disp=0)

            # 计算Pseudo R-squared (McFadden's R-squared)
            # R代码: 1 - (logLik(model)/logLik(null_model))
            null_model = OrderedModel(y, np.ones((len(y), 1)), distr='probit')
            null_result = null_model.fit(method='bfgs', maxiter=1000, disp=0)
            pseudo_r2 = 1 - (result.llf / null_result.llf)

            return {
                'model': result,
                'null_model': null_result,
                'pseudo_r2': pseudo_r2,
                'coefficients': result.params,
                'std_errors': result.bse,
                'pvalues': result.pvalues,
                'formula': f"{dep_var} ~ temp3 * pv + controls"
            }

        except Exception as e:
            print(f"有序Probit回归失败: {e}")
            return None

    def format_regression_results(self, result_dict, model_name):
        """格式化回归结果"""
        if result_dict is None:
            print(f"{model_name}: 回归失败")
            return

        print(f"\n{'='*80}")
        print(f"{model_name}")
        print('='*80)

        coeffs = result_dict['coefficients']
        std_errs = result_dict['std_errors']
        pvals = result_dict['pvalues']

        # 格式化输出
        print(f"{'变量':<20} {'系数':<12} {'标准误':<12} {'P值':<15} {'显著性'}")
        print('-' * 70)

        # 主要关注的变量
        key_vars = ['temp3', 'pv', 'temp3_pv']
        var_names = ['网络成瘾', '隐私感知', '网络成瘾:隐私感知']

        for var, name in zip(key_vars, var_names):
            if var in coeffs.index:
                coef = coeffs[var]
                se = std_errs[var]
                pval = pvals[var]
                sig = self.get_significance_stars(pval)

                print(f"{name:<20} {coef:<12.3f} {se:<12.3f} {pval:<15.3e} {sig}")

        # 模型拟合指标
        print(f"\nPseudo R-squared: {result_dict['pseudo_r2']:.6f}")

        return result_dict

    def get_significance_stars(self, pval):
        """获取显著性星号"""
        if pd.isna(pval):
            return ""
        elif pval < 0.001:
            return "***"
        elif pval < 0.01:
            return "**"
        elif pval < 0.05:
            return "*"
        elif pval < 0.1:
            return "."
        else:
            return ""

    def descriptive_statistics(self):
        """描述性统计"""
        print("\n" + "="*80)
        print("描述性统计")
        print("="*80)

        # 网络成瘾和隐私感知的描述性统计
        print(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，"
              f"sd:{self.final_data['temp3'].std():.1f}，"
              f"最小值{self.final_data['temp3'].min()}，"
              f"最大值{self.final_data['temp3'].max()}")

        print(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，"
              f"sd:{self.final_data['pv'].std():.2f}，"
              f"最小值{self.final_data['pv'].min()}，"
              f"最大值{self.final_data['pv'].max()}")

        # 因变量频数统计
        dep_vars = ['reject', 'parent', 'platform', 'law']
        dep_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        for var, name in zip(dep_vars, dep_names):
            if var in self.final_data.columns:
                print(f"\n{name}频数统计:")
                freq = self.final_data[var].value_counts().sort_index()
                print(freq.to_string())

        return self.final_data.describe()

    def run_complete_analysis(self):
        """运行完整分析 - 精确复现R代码的分析流程"""
        print("开始隐私保护行为分析 - 精确复现R语言有序Probit结果...")
        print("="*100)

        # 1. 加载和准备数据
        self.load_data()
        self.prepare_final_dataset()

        # 2. 描述性统计
        self.descriptive_statistics()

        # 3. 有序Probit回归分析 - 精确复现R代码
        # R代码中的因变量顺序：y1(reject), y2(parent), y3(platform), y4(law)
        dependent_vars = ['y1', 'y2', 'y3', 'y4']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        self.results = {}

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            print(f"\n{'='*100}")
            print(f"分析因变量: {dep_name} ({dep_var})")
            print('='*100)

            # 有序Probit回归 (含交互项) - 复现R的主要模型
            probit_with_int = self.run_ordered_probit_regression(dep_var, include_interaction=True)
            self.format_regression_results(probit_with_int, f"{dep_name} - 有序Probit回归 (含交互项)")

            # 有序Probit回归 (无交互项) - 复现R的对比模型
            probit_without_int = self.run_ordered_probit_regression(dep_var, include_interaction=False)
            self.format_regression_results(probit_without_int, f"{dep_name} - 有序Probit回归 (无交互项)")

            # 保存结果
            self.results[dep_var] = {
                'probit_with_interaction': probit_with_int,
                'probit_without_interaction': probit_without_int
            }

        return self.results

    def save_results_to_file(self, filename='improved_privacy_analysis_results.txt'):
        """保存结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("隐私保护行为分析结果 - 有序Probit模型精确复现\n")
            f.write("="*100 + "\n\n")

            # 描述性统计
            f.write("描述性统计\n")
            f.write("-"*50 + "\n")
            f.write(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，")
            f.write(f"sd:{self.final_data['temp3'].std():.1f}，")
            f.write(f"最小值{self.final_data['temp3'].min()}，")
            f.write(f"最大值{self.final_data['temp3'].max()}\n")

            f.write(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，")
            f.write(f"sd:{self.final_data['pv'].std():.2f}，")
            f.write(f"最小值{self.final_data['pv'].min()}，")
            f.write(f"最大值{self.final_data['pv'].max()}\n\n")

            # 回归结果
            dependent_vars = ['y1', 'y2', 'y3', 'y4']
            dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']
            original_vars = ['reject', 'parent', 'platform', 'law']

            for i, (dep_var, dep_name, orig_var) in enumerate(zip(dependent_vars, dep_var_names, original_vars)):
                if dep_var in self.results:
                    f.write(f"{i+1}. 因变量：{dep_name}\n")
                    f.write("-"*80 + "\n")

                    # 频数统计
                    if orig_var in self.final_data.columns:
                        freq = self.final_data[orig_var].value_counts().sort_index()
                        f.write("频数统计:\n")
                        for idx, count in freq.items():
                            f.write(f"  {idx}: {count}\n")

                    # 主要结果（含交互项）
                    result = self.results[dep_var]['probit_with_interaction']
                    if result:
                        coeffs = result['coefficients']
                        std_errs = result['std_errors']
                        pvals = result['pvalues']

                        f.write("\n有序Probit回归结果（含交互项）:\n")
                        f.write(f"{'变量':<20} {'系数':<12} {'标准误':<12} {'P值':<15}\n")
                        f.write("-" * 70 + "\n")

                        key_vars = ['temp3', 'pv', 'temp3_pv']
                        var_names = ['网络成瘾', '隐私感知', '网络成瘾:隐私感知']

                        for var, name in zip(key_vars, var_names):
                            if var in coeffs.index:
                                coef = coeffs[var]
                                se = std_errs[var]
                                pval = pvals[var]
                                sig = self.get_significance_stars(pval)
                                f.write(f"{name:<20} {coef:<12.3f} {se:<12.3f} {pval:<15.3e} {sig}\n")

                        f.write(f"\nPseudo R-squared: {result['pseudo_r2']:.6f}\n")

                    f.write("\n" + "="*100 + "\n\n")

        print(f"\n结果已保存到文件: {filename}")

    def create_summary_table(self):
        """创建汇总表"""
        print("\n" + "="*100)
        print("有序Probit回归汇总结果表格")
        print("="*100)

        # 创建汇总表
        summary_data = []

        dependent_vars = ['y1', 'y2', 'y3', 'y4']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            if dep_var in self.results:
                result = self.results[dep_var]['probit_with_interaction']
                if result:
                    coeffs = result['coefficients']
                    std_errs = result['std_errors']
                    pvals = result['pvalues']

                    # 提取关键系数
                    temp3_coef = coeffs.get('temp3', np.nan)
                    temp3_se = std_errs.get('temp3', np.nan)
                    temp3_pval = pvals.get('temp3', np.nan)

                    pv_coef = coeffs.get('pv', np.nan)
                    pv_se = std_errs.get('pv', np.nan)
                    pv_pval = pvals.get('pv', np.nan)

                    interaction_coef = coeffs.get('temp3_pv', np.nan)
                    interaction_se = std_errs.get('temp3_pv', np.nan)
                    interaction_pval = pvals.get('temp3_pv', np.nan)

                    summary_data.append({
                        '因变量': dep_name,
                        '网络成瘾系数': f"{temp3_coef:.3f}{self.get_significance_stars(temp3_pval)}",
                        '网络成瘾标准误': f"({temp3_se:.3f})",
                        '隐私感知系数': f"{pv_coef:.3f}{self.get_significance_stars(pv_pval)}",
                        '隐私感知标准误': f"({pv_se:.3f})",
                        '交互项系数': f"{interaction_coef:.3f}{self.get_significance_stars(interaction_pval)}",
                        '交互项标准误': f"({interaction_se:.3f})",
                        'Pseudo R²': f"{result['pseudo_r2']:.6f}"
                    })

        # 创建DataFrame并显示
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))

        # 保存汇总表
        summary_df.to_csv('improved_privacy_analysis_summary.csv', index=False, encoding='utf-8-sig')
        print(f"\n汇总表已保存为: improved_privacy_analysis_summary.csv")

        return summary_df


def main():
    """主函数"""
    data_path = '全国大学生网络素养数据.xlsx'

    try:
        print("隐私保护行为分析 - 有序Probit模型精确复现R语言结果")
        print("="*100)

        # 创建分析实例
        analysis = ImprovedPrivacyAnalysis(data_path)

        # 运行完整分析
        results = analysis.run_complete_analysis()

        # 创建汇总表
        analysis.create_summary_table()

        # 保存结果
        analysis.save_results_to_file()

        print("\n" + "="*100)
        print("分析完成！生成的文件:")
        print("- improved_privacy_analysis_results.txt (详细结果)")
        print("- improved_privacy_analysis_summary.csv (汇总表)")
        print("="*100)

        return analysis, results

    except FileNotFoundError:
        print(f"错误：找不到数据文件 {data_path}")
        return None, None
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    analysis, results = main()
