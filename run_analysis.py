#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的运行脚本 - 测试隐私保护行为分析
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """检查必要的包是否已安装"""
    required_packages = {
        'pandas': 'pandas',
        'numpy': 'numpy', 
        'matplotlib': 'matplotlib',
        'scipy': 'scipy',
        'statsmodels': 'statsmodels',
        'openpyxl': 'openpyxl'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} (缺失)")
    
    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        print("运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_data_file():
    """检查数据文件是否存在"""
    data_file = '全国大学生网络素养数据.xlsx'
    if os.path.exists(data_file):
        print(f"✓ 数据文件存在: {data_file}")
        return True
    else:
        print(f"✗ 数据文件不存在: {data_file}")
        print("请确保数据文件在当前目录下")
        return False

def run_simple_test():
    """运行简单测试"""
    try:
        from privacy_analysis_python import PrivacyAnalysis
        
        print("正在进行简单测试...")
        
        # 创建分析实例
        analysis = PrivacyAnalysis('全国大学生网络素养数据.xlsx')
        
        # 只加载数据和基本统计
        analysis.load_data()
        analysis.prepare_final_dataset()
        
        print("✓ 数据加载成功")
        print(f"✓ 最终数据集形状: {analysis.final_data.shape}")
        
        # 基本描述性统计
        analysis.descriptive_statistics()
        
        print("✓ 基本测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_analysis():
    """运行完整分析"""
    try:
        from privacy_analysis_python import main
        
        print("开始运行完整分析...")
        analysis, results = main()
        
        if analysis is not None:
            print("✓ 完整分析成功完成")
            return True
        else:
            print("✗ 完整分析失败")
            return False
            
    except Exception as e:
        print(f"✗ 完整分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("隐私保护行为分析 - 运行检查")
    print("="*50)
    
    # 1. 检查依赖包
    print("\n1. 检查Python包依赖:")
    if not check_requirements():
        print("请先安装缺失的包")
        sys.exit(1)
    
    # 2. 检查数据文件
    print("\n2. 检查数据文件:")
    if not check_data_file():
        print("请确保数据文件存在")
        sys.exit(1)
    
    # 3. 运行简单测试
    print("\n3. 运行简单测试:")
    if not run_simple_test():
        print("简单测试失败，请检查错误信息")
        sys.exit(1)
    
    # 4. 询问是否运行完整分析
    print("\n4. 是否运行完整分析？")
    response = input("输入 'y' 或 'yes' 运行完整分析，其他键跳过: ").lower().strip()
    
    if response in ['y', 'yes']:
        print("\n开始完整分析...")
        if run_full_analysis():
            print("\n🎉 所有分析完成！")
            print("请查看生成的结果文件:")
            print("- privacy_analysis_results.txt")
            print("- privacy_analysis_visualization.png")
        else:
            print("\n❌ 完整分析失败")
    else:
        print("\n跳过完整分析")
    
    print("\n分析脚本运行完毕")
