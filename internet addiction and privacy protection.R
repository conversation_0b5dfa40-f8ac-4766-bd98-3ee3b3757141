#internet addiction and privacy protection
#########################################################################################################################
#############################################----import dataset and package----##########################################

library(readxl)

library(psych)
library(ltm)
library(semTools)
library(lavaan)

data<-read_excel('全国大学生网络素养数据.xlsx')

##############################################----internet addiction----#################################################

temp2<-data[,c(147:164)]
colnames(temp2)<-c('n1'	,'n2'	,'n3'	,'n4'	,'n5'	,'n6'	,'n7'	,'n8'	,'n9'	,'n10'	,'n11'	,'n12'	,'n13'	,'n14'	,'n15'	,'n16'	,'n17'	,'n18')
temp2<-as.data.frame(scale(temp2,center = T,scale = T))#数据标准化
KMO(temp2)#数据KMO
cronbach.alpha(temp2)#数据克隆巴赫数值

privacy<-'
perception    =~ n1 +n2 +n3 +n4 +n5 +n6 +n7 +n8 +n9 +n10 +n11
action        =~ n12 +n13 +n14 +n15 +n16 +n17 +n18
total_privacy =~ perception +action
'
results2<- sem(privacy, data = temp2,se='bootstrap')
fitMeasures(results2,c("chisq","df","pvalue","cfi",'tli',"rmsea",'srmr',"nfi","ifi","EVCI"))#汇报SEM的拟合指标
round(reliability(results2),3)#omega为CR，avevar为AVE，alpha为cronbach's alpha，保留3个有效数字
scores_privacy <- lavPredict(results2,type='lv')#计算潜变量因子得分

View(summary(results2,fit.measures = TRUE, standard= TRUE)$pe)


#----网络成瘾计算----
temp3<-data[,c(70:92)]#网络成瘾量表对应70：90列
temp3<-apply(data[,c(75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91)], 1, sum)

temp2<-data[,c(147,149,150)]
colnames(temp2)<-c('n1','n2','n3')
model<-'
perception =~n1+n2+n3
'
re<-sem(model, data=temp2,se='boot')
summary(re,fit.measures = TRUE, standard= TRUE)
fitMeasures(re,c("chisq","df","pvalue","cfi",'tli',"rmsea",'srmr',"nfi","ifi","EVCI"))
pv<-apply(data[,c(147,149,150)], 1, mean)


#----regression----
library(MASS)#有序probit模型

library(sandwich)#稳健标准误
library(lmtest)

geography<-data[,c(7:14,43:45)]
colnames(geography)<-c('gender','location','university','school','major','edu_background','grade',"hukou",'edu_fa','edu_ma','income')

finaldta<-cbind(geography,temp3,pv,data[,c(161:164)])
colnames(finaldta)[14:17]<-c('reject','platform','parent','law')

factor_vars <- c('gender','location','university','school','major','edu_background','grade',"hukou",'edu_fa','edu_ma','income')
finaldta[, factor_vars] <- lapply(finaldta[, factor_vars], as.factor)



#------ordered probit model---------
y1<-factor(finaldta[,'reject'],levels =1:5, labels = c('low','medium','high','very high','most high') )
y2<-factor(finaldta[,'parent'],levels =1:5, labels = c('low','medium','high','very high','most high') )
y3<-factor(finaldta[,'platform'],levels =1:5, labels = c('low','medium','high','very high','most high') )
y4<-factor(finaldta[,'law'],levels =1:5, labels = c('low','medium','high','very high','most high') )

finaldta['y1']<-y1

model_probit <- polr(y1 ~ temp3*pv
                     + as.factor(gender)
                     + as.factor(major)
                     + as.factor(grade)

                     + as.factor(location)
                     + as.factor(hukou)
                     + as.factor(edu_fa)
                     + as.factor(edu_ma)
                     + as.factor(income)
                     , data = finaldta, Hess = TRUE, method='probit')


robust_vcov <- vcovCL(model_probit, type = "HC1")
robust_test <- coeftest(model_probit, vcov = robust_vcov)
round(robust_test,3)

# Null model
null_model <- polr(y1 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(model_probit)/logLik(null_model))
#chi-square
anova(null_model, model_probit)

model_probit_no_inter <- polr(y1 ~ temp3+ pv
                     + as.factor(gender)
                     + as.factor(major)
                     + as.factor(grade)

                     + as.factor(location)
                     + as.factor(hukou)
                     + as.factor(edu_fa)
                     + as.factor(edu_ma)
                     + as.factor(income)
                     , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_no_inter, type = "HC1")
round(coeftest(model_probit_no_inter, vcov = robust_vcov),3)
1 - (logLik(model_probit_no_inter)/logLik(null_model))
#chi-square
anova(null_model, model_probit_no_inter)

################################################################

model_probit_y2 <- polr(y2 ~ temp3*pv
                     + as.factor(gender)
                     + as.factor(major)
                     + as.factor(grade)

                     + as.factor(location)
                     + as.factor(hukou)
                     + as.factor(edu_fa)
                     + as.factor(edu_ma)
                     + as.factor(income)
                     , data = finaldta, Hess = TRUE, method='probit')


robust_vcov <- vcovCL(model_probit_y2, type = "HC1")
robust_test <- coeftest(model_probit_y2, vcov = robust_vcov)
round(robust_test,3)

# Null model
null_model <- polr(y2 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_y2 )/logLik(null_model))
#chi-square
anova(null_model,       model_probit_y2 )

model_probit_no_inter_y2 <- polr(y2 ~ temp3+ pv
                              + as.factor(gender)
                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_no_inter_y2, type = "HC1")
round(coeftest(model_probit_no_inter_y2, vcov = robust_vcov),3)
1 - (logLik(model_probit_no_inter_y2)/logLik(null_model))
#chi-square
anova(null_model, model_probit_no_inter_y2)


###############################################################################
model_probit_y3 <- polr(y3 ~ temp3*pv
                        + as.factor(gender)
                        + as.factor(major)
                        + as.factor(grade)

                        + as.factor(location)
                        + as.factor(hukou)
                        + as.factor(edu_fa)
                        + as.factor(edu_ma)
                        + as.factor(income)
                        , data = finaldta, Hess = TRUE, method='probit')


robust_vcov <- vcovCL(model_probit_y3, type = "HC1")
robust_test <- coeftest(model_probit_y3, vcov = robust_vcov)
round(robust_test,3)

# Null model
null_model <- polr(y3 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_y3 )/logLik(null_model))
#chi-square
anova(null_model,       model_probit_y3 )

model_probit_no_inter_y3 <- polr(y3 ~ temp3+ pv
                                 + as.factor(gender)
                                 + as.factor(major)
                                 + as.factor(grade)

                                 + as.factor(location)
                                 + as.factor(hukou)
                                 + as.factor(edu_fa)
                                 + as.factor(edu_ma)
                                 + as.factor(income)
                                 , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_no_inter_y3, type = "HC1")
round(coeftest(model_probit_no_inter_y3, vcov = robust_vcov),3)
1 - (logLik(model_probit_no_inter_y3)/logLik(null_model))
#chi-square
anova(null_model, model_probit_no_inter_y3)



##########################################
model_probit_y4 <- polr(y4 ~ temp3*pv
                        + as.factor(gender)
                        + as.factor(major)
                        + as.factor(grade)

                        + as.factor(location)
                        + as.factor(hukou)
                        + as.factor(edu_fa)
                        + as.factor(edu_ma)
                        + as.factor(income)
                        , data = finaldta, Hess = TRUE, method='probit')


robust_vcov <- vcovCL(model_probit_y4, type = "HC1")
robust_test <- coeftest(model_probit_y4, vcov = robust_vcov)
round(robust_test,3)

# Null model
null_model <- polr(y4 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_y4 )/logLik(null_model))
#chi-square
anova(null_model,       model_probit_y4 )

model_probit_no_inter_y4 <- polr(y4 ~ temp3+ pv
                                 + as.factor(gender)
                                 + as.factor(major)
                                 + as.factor(grade)

                                 + as.factor(location)
                                 + as.factor(hukou)
                                 + as.factor(edu_fa)
                                 + as.factor(edu_ma)
                                 + as.factor(income)
                                 , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_no_inter_y4, type = "HC1")
round(coeftest(model_probit_no_inter_y4, vcov = robust_vcov),3)
1 - (logLik(model_probit_no_inter_y4)/logLik(null_model))
#chi-square
anova(null_model, model_probit_no_inter_y4)
##################################################################OLS regression############################

ols_y1<-lm(finaldta[,'reject'] ~
             temp3* pv
           + as.factor(gender)
           + as.factor(major)
           + as.factor(grade)

           + as.factor(location)
           + as.factor(hukou)
           + as.factor(edu_fa)
           + as.factor(edu_ma)
           + as.factor(income)
           , data = finaldta)
robust_se <- vcovHC(ols_y1, type = "HC1") # HC1 is one of the types for robust standard errors
round(coeftest(ols_y1, robust_se),3)
summary(ols_y1)

ols_y2<-lm(finaldta[,'parent'] ~
             temp3* pv
           + as.factor(gender)
           + as.factor(major)
           + as.factor(grade)

           + as.factor(location)
           + as.factor(hukou)
           + as.factor(edu_fa)
           + as.factor(edu_ma)
           + as.factor(income)
           , data = finaldta)
robust_se <- vcovHC(ols_y2, type = "HC1") # HC1 is one of the types for robust standard errors
round(coeftest(ols_y2, robust_se),3)
summary(ols_y2)


ols_y3<-lm(finaldta[,'platform'] ~
             temp3* pv
           + as.factor(gender)
           + as.factor(major)
           + as.factor(grade)

           + as.factor(location)
           + as.factor(hukou)
           + as.factor(edu_fa)
           + as.factor(edu_ma)
           + as.factor(income)
           , data = finaldta)
robust_se <- vcovHC(ols_y3, type = "HC1") # HC1 is one of the types for robust standard errors
round(coeftest(ols_y3, robust_se),3)
summary(ols_y3)

ols_y4<-lm(finaldta[,'law'] ~
             temp3* pv
           + as.factor(gender)
           + as.factor(major)
           + as.factor(grade)

           + as.factor(location)
           + as.factor(hukou)
           + as.factor(edu_fa)
           + as.factor(edu_ma)
           + as.factor(income)
           , data = finaldta)
robust_se <- vcovHC(ols_y4, type = "HC1") # HC1 is one of the types for robust standard errors
round(coeftest(ols_y4, robust_se),3)
summary(ols_y4)

###############################################################Ordered logit model######################
model_logit_y1 <- polr(y1 ~ temp3*pv
                        + as.factor(gender)
                        + as.factor(major)
                        + as.factor(grade)

                        + as.factor(location)
                        + as.factor(hukou)
                        + as.factor(edu_fa)
                        + as.factor(edu_ma)
                        + as.factor(income)
                        , data = finaldta, Hess = TRUE, method='logistic')


robust_vcov <- vcovCL(model_logit_y1 , type = "HC1")
robust_test <- coeftest(model_logit_y1 , vcov = robust_vcov)
round(robust_test,3)
# Null model
null_model <- polr(y1 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_logit_y1  )/logLik(null_model))
#chi-square
anova(null_model,   model_logit_y1  )

model_logit_y2 <- polr(y2 ~ temp3*pv
                       + as.factor(gender)
                       + as.factor(major)
                       + as.factor(grade)

                       + as.factor(location)
                       + as.factor(hukou)
                       + as.factor(edu_fa)
                       + as.factor(edu_ma)
                       + as.factor(income)
                       , data = finaldta, Hess = TRUE, method='logistic')


robust_vcov <- vcovCL(model_logit_y2 , type = "HC1")
robust_test <- coeftest(model_logit_y2 , vcov = robust_vcov)
round(robust_test,3)
# Null model
null_model <- polr(y2 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_logit_y2  )/logLik(null_model))
#chi-square
anova(null_model,   model_logit_y2  )


model_logit_y3 <- polr(y3 ~ temp3*pv
                       + as.factor(gender)
                       + as.factor(major)
                       + as.factor(grade)

                       + as.factor(location)
                       + as.factor(hukou)
                       + as.factor(edu_fa)
                       + as.factor(edu_ma)
                       + as.factor(income)
                       , data = finaldta, Hess = TRUE, method='logistic')


robust_vcov <- vcovCL(model_logit_y3 , type = "HC1")
robust_test <- coeftest(model_logit_y3 , vcov = robust_vcov)
round(robust_test,3)
# Null model
null_model <- polr(y3 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_logit_y3  )/logLik(null_model))
#chi-square
anova(null_model,   model_logit_y3  )


model_logit_y4 <- polr(y4 ~ temp3*pv
                       + as.factor(gender)
                       + as.factor(major)
                       + as.factor(grade)

                       + as.factor(location)
                       + as.factor(hukou)
                       + as.factor(edu_fa)
                       + as.factor(edu_ma)
                       + as.factor(income)
                       , data = finaldta, Hess = TRUE, method='logistic')


robust_vcov <- vcovCL(model_logit_y4 , type = "HC1")
robust_test <- coeftest(model_logit_y4 , vcov = robust_vcov)
round(robust_test,3)
# Null model
null_model <- polr(y4 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_logit_y4  )/logLik(null_model))
#chi-square
anova(null_model,   model_logit_y4 )
############################################################heterogeneity analysis#############
###########across gender

model_probit_heter_y1 <- polr(y1 ~ temp3*pv + temp3:pv:as.factor(gender)
                                  + as.factor(gender)
                                  +temp3:as.factor(gender)

                                 + as.factor(major)
                                 + as.factor(grade)

                                 + as.factor(location)
                                 + as.factor(hukou)
                                 + as.factor(edu_fa)
                                 + as.factor(edu_ma)
                                 + as.factor(income)
                                 , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y1, type = "HC1")
round(coeftest(model_probit_heter_y1, vcov = robust_vcov),3)
# Null model
null_model <- polr(y1 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y1  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y1 )

model_probit_heter_y2 <- polr(y2 ~ temp3*pv + temp3:pv:as.factor(gender)
                              + as.factor(gender)
                              +temp3:as.factor(gender)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y2, type = "HC1")
round(coeftest(model_probit_heter_y2, vcov = robust_vcov),3)
# Null model
null_model <- polr(y2 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y2  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y2 )

model_probit_heter_y3 <- polr(y3 ~ temp3*pv + temp3:pv:as.factor(gender)
                              + as.factor(gender)
                              +temp3:as.factor(gender)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y3, type = "HC1")
round(coeftest(model_probit_heter_y3, vcov = robust_vcov),3)
# Null model
null_model <- polr(y3 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y3  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y3 )

model_probit_heter_y4 <- polr(y4 ~ temp3*pv + temp3:pv:as.factor(gender)
                              + as.factor(gender)
                              +temp3:as.factor(gender)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y4, type = "HC1")
round(coeftest(model_probit_heter_y4, vcov = robust_vcov),3)
# Null model
null_model <- polr(y4 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y4  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y4 )
#######################################across hukou

model_probit_heter_y1 <- polr(y1 ~ temp3*pv + temp3:pv:as.factor(hukou)
                              + as.factor(gender)
                              +temp3:as.factor(hukou)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y1, type = "HC1")
round(coeftest(model_probit_heter_y1, vcov = robust_vcov),3)
# Null model
null_model <- polr(y1 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y1  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y1 )

model_probit_heter_y2 <- polr(y2 ~ temp3*pv + temp3:pv:as.factor(hukou)
                              + as.factor(gender)
                              +temp3:as.factor(hukou)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y2, type = "HC1")
round(coeftest(model_probit_heter_y2, vcov = robust_vcov),3)
# Null model
null_model <- polr(y2 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y2  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y2 )

model_probit_heter_y3 <- polr(y3 ~ temp3*pv + temp3:pv:as.factor(hukou)
                              + as.factor(gender)
                              +temp3:as.factor(hukou)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y3, type = "HC1")
round(coeftest(model_probit_heter_y3, vcov = robust_vcov),3)
# Null model
null_model <- polr(y3 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y3  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y3 )

model_probit_heter_y4 <- polr(y4 ~ temp3*pv + temp3:pv:as.factor(hukou)
                              + as.factor(gender)
                              +temp3:as.factor(hukou)

                              + as.factor(major)
                              + as.factor(grade)

                              + as.factor(location)
                              + as.factor(hukou)
                              + as.factor(edu_fa)
                              + as.factor(edu_ma)
                              + as.factor(income)
                              , data = finaldta, Hess = TRUE, method='probit')
robust_vcov <- vcovCL(model_probit_heter_y4, type = "HC1")
round(coeftest(model_probit_heter_y4, vcov = robust_vcov),3)
# Null model
null_model <- polr(y4 ~ 1, data = finaldta, method = "probit")
# McFadden's R-square
1 - (logLik(  model_probit_heter_y4  )/logLik(null_model))
#chi-square
anova(null_model,   model_probit_heter_y4 )

#################################################################################################
dv_order<-c('y1','y2','y3','y4')
fit_model <- function(dep_var, data) {
  formula_str <- paste(
    dep_var, "~ temp3 * pv + as.factor(gender) + as.factor(major) + as.factor(grade) +",
    "as.factor(location) + as.factor(hukou) + as.factor(edu_fa) + as.factor(edu_ma) + as.factor(income)"
  )
  print(formula_str)  # 检查公式字符串是否正确
  formula <- as.formula(formula_str)
  model <- polr(formula, data = data, Hess = TRUE, method = 'probit')
  robust_vcov <- vcovCL(model, type = "HC1")
  robust_test <- round(coeftest(model, vcov = robust_vcov), 3)
  list(model = model, robust_test = robust_test)
}

model_results <- lapply(dv_order, fit_model, data = finaldta)
#----no interaction, only main effect----
fit_model_main <- function(dep_var, data) {
  formula_str <- paste(
    dep_var, "~ temp3 + pv + as.factor(gender) + as.factor(major) + as.factor(grade) +",
    "as.factor(location) + as.factor(hukou) + as.factor(edu_fa) + as.factor(edu_ma) + as.factor(income)"
  )
  print(formula_str)  # 检查公式字符串是否正确
  formula <- as.formula(formula_str)
  model <- polr(formula, data = data, Hess = TRUE, method = 'probit')
  robust_vcov <- vcovCL(model, type = "HC1")
  robust_test <- round(coeftest(model, vcov = robust_vcov), 3)
  list(model = model, robust_test = robust_test)
}

model_results_main <- lapply(dv_order, fit_model_main, data = finaldta)
model_results_main[1]
model_results_main[2]
model_results_main[3]
model_results_main[4]
#-------descriptive statistic------
table(finaldta$gender)/7904
table(finaldta$location)/7904
round(table(finaldta$grade)/7904,3)
table(finaldta$hukou)/7904

