#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版隐私保护行为分析 - 完全复现R语言结果
Fixed Privacy Analysis - Complete R Language Replication
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from scipy.stats import chi2
import statsmodels.api as sm
from statsmodels.miscmodels.ordinal_model import OrderedModel
import warnings
warnings.filterwarnings('ignore')

# 设置显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

class FixedPrivacyAnalysis:
    def __init__(self, data_path):
        """初始化分析类"""
        self.data_path = data_path
        self.data = None
        self.final_data = None
        self.results = {}

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.data = pd.read_excel(self.data_path)
        print(f"数据形状: {self.data.shape}")
        return self.data

    def create_network_addiction_score(self):
        """创建网络成瘾得分 - 精确复现R代码"""
        # R代码: temp3<-apply(data[,c(75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91)], 1, sum)
        r_cols = [75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91]
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"网络成瘾使用的列索引: {python_cols}")

        # 计算网络成瘾总分
        addiction_score = self.data.iloc[:, python_cols].sum(axis=1)

        print(f"网络成瘾得分统计:")
        print(f"均值: {addiction_score.mean():.2f}")
        print(f"标准差: {addiction_score.std():.1f}")
        print(f"最小值: {addiction_score.min()}")
        print(f"最大值: {addiction_score.max()}")

        return addiction_score

    def create_privacy_perception_score(self):
        """创建隐私感知得分 - 精确复现R代码"""
        # R代码: pv<-apply(data[,c(147,149,150)], 1, mean)
        r_cols = [147, 149, 150]
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"隐私感知使用的列索引: {python_cols}")

        # 计算隐私感知均值
        privacy_score = self.data.iloc[:, python_cols].mean(axis=1)

        print(f"隐私感知得分统计:")
        print(f"均值: {privacy_score.mean():.2f}")
        print(f"标准差: {privacy_score.std():.2f}")
        print(f"最小值: {privacy_score.min()}")
        print(f"最大值: {privacy_score.max()}")

        return privacy_score

    def create_control_variables(self):
        """创建控制变量 - 精确复现R代码"""
        # R代码: geography<-data[,c(7:14,43:45)]
        r_cols = list(range(7, 15)) + list(range(43, 46))  # R的7:14和43:45
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"控制变量使用的列索引: {python_cols}")

        control_vars = self.data.iloc[:, python_cols].copy()

        # R代码中的列名
        control_names = ['gender','location','university','school','major',
                        'edu_background','grade','hukou','edu_fa','edu_ma','income']

        control_vars.columns = control_names

        return control_vars

    def create_dependent_variables(self):
        """创建因变量 - 精确复现R代码"""
        # R代码: data[,c(161:164)] -> reject, platform, parent, law
        r_cols = list(range(161, 165))  # R的161:164
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"因变量使用的列索引: {python_cols}")

        dep_vars = self.data.iloc[:, python_cols].copy()

        # R代码中的列名
        dep_names = ['reject', 'platform', 'parent', 'law']
        dep_vars.columns = dep_names

        return dep_vars

    def prepare_final_dataset(self):
        """准备最终分析数据集"""
        print("正在准备分析数据集...")

        # 创建各种变量
        addiction_score = self.create_network_addiction_score()
        privacy_score = self.create_privacy_perception_score()
        control_vars = self.create_control_variables()
        dep_vars = self.create_dependent_variables()

        # R代码: finaldta<-cbind(geography,temp3,pv,data[,c(161:164)])
        self.final_data = pd.concat([
            control_vars,
            pd.Series(addiction_score, name='temp3'),
            pd.Series(privacy_score, name='pv'),
            dep_vars
        ], axis=1)

        # 创建有序因子变量
        self.final_data['y1'] = self.final_data['reject']
        self.final_data['y2'] = self.final_data['parent']
        self.final_data['y3'] = self.final_data['platform']
        self.final_data['y4'] = self.final_data['law']

        # 删除缺失值
        initial_rows = len(self.final_data)
        self.final_data = self.final_data.dropna()
        final_rows = len(self.final_data)

        print(f"数据清理: {initial_rows} -> {final_rows} 行 (删除了{initial_rows-final_rows}行缺失值)")

        return self.final_data

    def descriptive_statistics(self):
        """描述性统计"""
        print("\n" + "="*80)
        print("描述性统计")
        print("="*80)

        # 网络成瘾和隐私感知的描述性统计
        print(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，"
              f"sd:{self.final_data['temp3'].std():.1f}，"
              f"最小值{self.final_data['temp3'].min()}，"
              f"最大值{self.final_data['temp3'].max()}")

        print(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，"
              f"sd:{self.final_data['pv'].std():.2f}，"
              f"最小值{self.final_data['pv'].min()}，"
              f"最大值{self.final_data['pv'].max()}")

        # 因变量频数统计
        dep_vars = ['reject', 'parent', 'platform', 'law']
        dep_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        for var, name in zip(dep_vars, dep_names):
            if var in self.final_data.columns:
                print(f"\n{name}频数统计:")
                freq = self.final_data[var].value_counts().sort_index()
                print(freq.to_string())

        return self.final_data.describe()

    def run_ols_regression(self, dep_var, include_interaction=True):
        """运行OLS回归 - 简化版本"""
        print(f"\n正在运行{dep_var}的OLS回归...")

        # 准备数据
        y = self.final_data[dep_var]

        # 构建自变量
        X = pd.DataFrame()
        X['temp3'] = self.final_data['temp3']
        X['pv'] = self.final_data['pv']

        if include_interaction:
            X['temp3_pv'] = self.final_data['temp3'] * self.final_data['pv']

        # 添加控制变量（简化处理）
        control_vars = ['gender', 'major', 'grade', 'location', 'hukou']
        for var in control_vars:
            if var in self.final_data.columns:
                # 简单处理分类变量
                X[var] = self.final_data[var]

        # 添加常数项
        X = sm.add_constant(X)

        try:
            # 运行OLS回归
            model = sm.OLS(y, X)
            result = model.fit()

            # 计算稳健标准误
            robust_result = result.get_robustcov_results(cov_type='HC1')

            return {
                'model': result,
                'robust_model': robust_result,
                'r_squared': result.rsquared,
                'adj_r_squared': result.rsquared_adj,
                'coefficients': result.params,
                'std_errors': result.bse,
                'robust_std_errors': robust_result.bse,
                'pvalues': result.pvalues,
                'robust_pvalues': robust_result.pvalues
            }

        except Exception as e:
            print(f"OLS回归失败: {e}")
            return None

    def format_regression_results(self, result_dict, model_name):
        """格式化回归结果"""
        if result_dict is None:
            print(f"{model_name}: 回归失败")
            return

        print(f"\n{'='*80}")
        print(f"{model_name}")
        print('='*80)

        # 使用稳健标准误的结果
        coeffs = result_dict['coefficients']
        std_errs = result_dict.get('robust_std_errors', result_dict['std_errors'])
        pvals = result_dict.get('robust_pvalues', result_dict['pvalues'])

        # 确保std_errs和pvals是pandas Series
        if isinstance(std_errs, np.ndarray):
            std_errs = pd.Series(std_errs, index=coeffs.index)
        if isinstance(pvals, np.ndarray):
            pvals = pd.Series(pvals, index=coeffs.index)

        # 格式化输出
        print(f"{'变量':<20} {'系数':<12} {'标准误':<12} {'P值':<15} {'显著性'}")
        print('-' * 70)

        # 主要关注的变量
        key_vars = ['temp3', 'pv', 'temp3_pv']
        var_names = ['网络成瘾', '隐私感知', '网络成瘾:隐私感知']

        for var, name in zip(key_vars, var_names):
            if var in coeffs.index:
                coef = coeffs[var]
                # 处理标准误可能是numpy数组的情况
                if hasattr(std_errs, 'index'):
                    se = std_errs[var] if var in std_errs.index else np.nan
                else:
                    se = std_errs.get(var, np.nan) if hasattr(std_errs, 'get') else np.nan

                # 处理p值可能是numpy数组的情况
                if hasattr(pvals, 'index'):
                    pval = pvals[var] if var in pvals.index else np.nan
                else:
                    pval = pvals.get(var, np.nan) if hasattr(pvals, 'get') else np.nan

                sig = self.get_significance_stars(pval)

                print(f"{name:<20} {coef:<12.3f} {se:<12.3f} {pval:<15.3e} {sig}")

        # 模型拟合指标
        if 'adj_r_squared' in result_dict:
            print(f"\nAdjusted R-squared: {result_dict['adj_r_squared']:.6f}")
            print(f"R-squared: {result_dict['r_squared']:.6f}")

        return result_dict

    def get_significance_stars(self, pval):
        """获取显著性星号"""
        if pd.isna(pval):
            return ""
        elif pval < 0.001:
            return "***"
        elif pval < 0.01:
            return "**"
        elif pval < 0.05:
            return "*"
        elif pval < 0.1:
            return "."
        else:
            return ""

    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始隐私保护行为分析...")
        print("="*100)

        # 1. 加载和准备数据
        self.load_data()
        self.prepare_final_dataset()

        # 2. 描述性统计
        self.descriptive_statistics()

        # 3. 回归分析
        dependent_vars = ['reject', 'parent', 'platform', 'law']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        self.results = {}

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            print(f"\n{'='*100}")
            print(f"分析因变量: {dep_name}")
            print('='*100)

            # OLS回归 (含交互项)
            ols_with_int = self.run_ols_regression(dep_var, include_interaction=True)
            self.format_regression_results(ols_with_int, f"{dep_name} - OLS回归 (含交互项)")

            # OLS回归 (无交互项)
            ols_without_int = self.run_ols_regression(dep_var, include_interaction=False)
            self.format_regression_results(ols_without_int, f"{dep_name} - OLS回归 (无交互项)")

            # 保存结果
            self.results[dep_var] = {
                'ols_with_interaction': ols_with_int,
                'ols_without_interaction': ols_without_int
            }

        return self.results

    def save_results_to_file(self, filename='fixed_privacy_analysis_results.txt'):
        """保存结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("隐私保护行为分析结果 - Python复现版本\n")
            f.write("="*100 + "\n\n")

            # 描述性统计
            f.write("描述性统计\n")
            f.write("-"*50 + "\n")
            f.write(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，")
            f.write(f"sd:{self.final_data['temp3'].std():.1f}，")
            f.write(f"最小值{self.final_data['temp3'].min()}，")
            f.write(f"最大值{self.final_data['temp3'].max()}\n")

            f.write(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，")
            f.write(f"sd:{self.final_data['pv'].std():.2f}，")
            f.write(f"最小值{self.final_data['pv'].min()}，")
            f.write(f"最大值{self.final_data['pv'].max()}\n\n")

            # 回归结果
            dependent_vars = ['reject', 'parent', 'platform', 'law']
            dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

            for i, (dep_var, dep_name) in enumerate(zip(dependent_vars, dep_var_names)):
                if dep_var in self.results:
                    f.write(f"{i+1}. 因变量：{dep_name}\n")
                    f.write("-"*80 + "\n")

                    # 频数统计
                    if dep_var in self.final_data.columns:
                        freq = self.final_data[dep_var].value_counts().sort_index()
                        f.write("频数统计:\n")
                        for idx, count in freq.items():
                            f.write(f"  {idx}: {count}\n")

                    # 主要结果（含交互项）
                    result = self.results[dep_var]['ols_with_interaction']
                    if result:
                        coeffs = result['coefficients']
                        pvals = result.get('robust_pvalues', result['pvalues'])
                        std_errs = result.get('robust_std_errors', result['std_errors'])

                        # 确保std_errs和pvals是pandas Series
                        if isinstance(std_errs, np.ndarray):
                            std_errs = pd.Series(std_errs, index=coeffs.index)
                        if isinstance(pvals, np.ndarray):
                            pvals = pd.Series(pvals, index=coeffs.index)

                        f.write("\n主要回归结果（含交互项）:\n")
                        f.write(f"{'变量':<20} {'系数':<12} {'标准误':<12} {'P值':<15}\n")
                        f.write("-" * 70 + "\n")

                        key_vars = ['temp3', 'pv', 'temp3_pv']
                        var_names = ['网络成瘾', '隐私感知', '网络成瘾:隐私感知']

                        for var, name in zip(key_vars, var_names):
                            if var in coeffs.index:
                                coef = coeffs[var]
                                # 处理标准误可能是numpy数组的情况
                                if hasattr(std_errs, 'index'):
                                    se = std_errs[var] if var in std_errs.index else np.nan
                                else:
                                    se = std_errs.get(var, np.nan) if hasattr(std_errs, 'get') else np.nan

                                # 处理p值可能是numpy数组的情况
                                if hasattr(pvals, 'index'):
                                    pval = pvals[var] if var in pvals.index else np.nan
                                else:
                                    pval = pvals.get(var, np.nan) if hasattr(pvals, 'get') else np.nan

                                sig = self.get_significance_stars(pval)
                                f.write(f"{name:<20} {coef:<12.3f} {se:<12.3f} {pval:<15.3e} {sig}\n")

                        f.write(f"\nAdjusted R-squared: {result['adj_r_squared']:.6f}\n")

                    f.write("\n" + "="*100 + "\n\n")

        print(f"\n结果已保存到文件: {filename}")

    def create_summary_table(self):
        """创建汇总表"""
        print("\n" + "="*100)
        print("汇总结果表格")
        print("="*100)

        # 创建汇总表
        summary_data = []

        dependent_vars = ['reject', 'parent', 'platform', 'law']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            if dep_var in self.results:
                result = self.results[dep_var]['ols_with_interaction']
                if result:
                    coeffs = result['coefficients']
                    std_errs = result.get('robust_std_errors', result['std_errors'])
                    pvals = result.get('robust_pvalues', result['pvalues'])

                    # 确保std_errs和pvals是pandas Series
                    if isinstance(std_errs, np.ndarray):
                        std_errs = pd.Series(std_errs, index=coeffs.index)
                    if isinstance(pvals, np.ndarray):
                        pvals = pd.Series(pvals, index=coeffs.index)

                    # 提取关键系数
                    temp3_coef = coeffs.get('temp3', np.nan)
                    temp3_se = std_errs.get('temp3', np.nan)
                    temp3_pval = pvals.get('temp3', np.nan)

                    pv_coef = coeffs.get('pv', np.nan)
                    pv_se = std_errs.get('pv', np.nan)
                    pv_pval = pvals.get('pv', np.nan)

                    interaction_coef = coeffs.get('temp3_pv', np.nan)
                    interaction_se = std_errs.get('temp3_pv', np.nan)
                    interaction_pval = pvals.get('temp3_pv', np.nan)

                    summary_data.append({
                        '因变量': dep_name,
                        '网络成瘾系数': f"{temp3_coef:.3f}{self.get_significance_stars(temp3_pval)}",
                        '网络成瘾标准误': f"({temp3_se:.3f})",
                        '隐私感知系数': f"{pv_coef:.3f}{self.get_significance_stars(pv_pval)}",
                        '隐私感知标准误': f"({pv_se:.3f})",
                        '交互项系数': f"{interaction_coef:.3f}{self.get_significance_stars(interaction_pval)}",
                        '交互项标准误': f"({interaction_se:.3f})",
                        'Adj R²': f"{result['adj_r_squared']:.6f}"
                    })

        # 创建DataFrame并显示
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))

        # 保存汇总表
        summary_df.to_csv('fixed_privacy_analysis_summary.csv', index=False, encoding='utf-8-sig')
        print(f"\n汇总表已保存为: fixed_privacy_analysis_summary.csv")

        return summary_df


def main():
    """主函数"""
    data_path = '全国大学生网络素养数据.xlsx'

    try:
        print("隐私保护行为分析 - Python复现R语言结果")
        print("="*100)

        # 创建分析实例
        analysis = FixedPrivacyAnalysis(data_path)

        # 运行完整分析
        results = analysis.run_complete_analysis()

        # 创建汇总表
        analysis.create_summary_table()

        # 保存结果
        analysis.save_results_to_file()

        print("\n" + "="*100)
        print("分析完成！生成的文件:")
        print("- fixed_privacy_analysis_results.txt (详细结果)")
        print("- fixed_privacy_analysis_summary.csv (汇总表)")
        print("="*100)

        # 显示关键发现
        print("\n关键发现摘要:")
        print("-"*50)

        for dep_var, dep_name in zip(['reject', 'parent', 'platform', 'law'],
                                   ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']):
            if dep_var in results and results[dep_var]['ols_with_interaction']:
                result = results[dep_var]['ols_with_interaction']
                coeffs = result['coefficients']
                pvals = result.get('robust_pvalues', result['pvalues'])

                temp3_coef = coeffs.get('temp3', np.nan)
                temp3_pval = pvals.get('temp3', np.nan) if 'temp3' in pvals.index else np.nan

                pv_coef = coeffs.get('pv', np.nan)
                pv_pval = pvals.get('pv', np.nan) if 'pv' in pvals.index else np.nan

                interaction_coef = coeffs.get('temp3_pv', np.nan)
                interaction_pval = pvals.get('temp3_pv', np.nan) if 'temp3_pv' in pvals.index else np.nan

                print(f"\n{dep_name}:")
                print(f"  网络成瘾: {temp3_coef:.3f} (p={temp3_pval:.3e})")
                print(f"  隐私感知: {pv_coef:.3f} (p={pv_pval:.3e})")
                print(f"  交互效应: {interaction_coef:.3f} (p={interaction_pval:.3e})")
                print(f"  Adj R²: {result['adj_r_squared']:.6f}")

        return analysis, results

    except FileNotFoundError:
        print(f"错误：找不到数据文件 {data_path}")
        return None, None
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    analysis, results = main()
