#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全复现R语言隐私保护行为分析
Internet Addiction and Privacy Protection Analysis - Complete Python Replication
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2
import statsmodels.api as sm
from statsmodels.miscmodels.ordinal_model import OrderedModel
from statsmodels.stats.sandwich_covariance import cov_hc1
from statsmodels.stats.stattools import durbin_watson
from statsmodels.regression.linear_model import OLS
import warnings
warnings.filterwarnings('ignore')

# 设置显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

class CompletePrivacyAnalysis:
    def __init__(self, data_path):
        """初始化分析类"""
        self.data_path = data_path
        self.data = None
        self.final_data = None
        self.results = {}

    def load_data(self):
        """加载数据 - 对应R: data<-read_excel('全国大学生网络素养数据.xlsx')"""
        print("正在加载数据...")
        self.data = pd.read_excel(self.data_path)
        print(f"数据形状: {self.data.shape}")
        return self.data

    def create_network_addiction_score(self):
        """创建网络成瘾得分 - 精确复现R代码"""
        # R代码: temp3<-apply(data[,c(75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91)], 1, sum)
        # 注意：R索引从1开始，Python从0开始，所以需要减1
        r_cols = [75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91]
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"网络成瘾使用的列索引 (Python): {python_cols}")

        # 计算网络成瘾总分
        addiction_score = self.data.iloc[:, python_cols].sum(axis=1)

        print(f"网络成瘾得分统计:")
        print(f"均值: {addiction_score.mean():.2f}")
        print(f"标准差: {addiction_score.std():.1f}")
        print(f"最小值: {addiction_score.min()}")
        print(f"最大值: {addiction_score.max()}")

        return addiction_score

    def create_privacy_perception_score(self):
        """创建隐私感知得分 - 精确复现R代码"""
        # R代码: pv<-apply(data[,c(147,149,150)], 1, mean)
        # Python索引: 146, 148, 149
        r_cols = [147, 149, 150]
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"隐私感知使用的列索引 (Python): {python_cols}")

        # 计算隐私感知均值
        privacy_score = self.data.iloc[:, python_cols].mean(axis=1)

        print(f"隐私感知得分统计:")
        print(f"均值: {privacy_score.mean():.2f}")
        print(f"标准差: {privacy_score.std():.2f}")
        print(f"最小值: {privacy_score.min()}")
        print(f"最大值: {privacy_score.max()}")

        return privacy_score

    def create_control_variables(self):
        """创建控制变量 - 精确复现R代码"""
        # R代码: geography<-data[,c(7:14,43:45)]
        # Python索引: 6:14, 42:45
        r_cols = list(range(7, 15)) + list(range(43, 46))  # R的7:14和43:45
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"控制变量使用的列索引 (Python): {python_cols}")

        control_vars = self.data.iloc[:, python_cols].copy()

        # R代码中的列名
        control_names = ['gender','location','university','school','major',
                        'edu_background','grade','hukou','edu_fa','edu_ma','income']

        control_vars.columns = control_names

        return control_vars

    def create_dependent_variables(self):
        """创建因变量 - 精确复现R代码"""
        # R代码: data[,c(161:164)] -> reject, platform, parent, law
        # Python索引: 160:164
        r_cols = list(range(161, 165))  # R的161:164
        python_cols = [col - 1 for col in r_cols]  # 转换为Python索引

        print(f"因变量使用的列索引 (Python): {python_cols}")

        dep_vars = self.data.iloc[:, python_cols].copy()

        # R代码中的列名
        dep_names = ['reject', 'platform', 'parent', 'law']
        dep_vars.columns = dep_names

        return dep_vars

    def prepare_final_dataset(self):
        """准备最终分析数据集 - 精确复现R代码"""
        print("正在准备分析数据集...")

        # 创建各种变量
        addiction_score = self.create_network_addiction_score()
        privacy_score = self.create_privacy_perception_score()
        control_vars = self.create_control_variables()
        dep_vars = self.create_dependent_variables()

        # R代码: finaldta<-cbind(geography,temp3,pv,data[,c(161:164)])
        self.final_data = pd.concat([
            control_vars,
            pd.Series(addiction_score, name='temp3'),
            pd.Series(privacy_score, name='pv'),
            dep_vars
        ], axis=1)

        # R代码: factor_vars <- c('gender','location','university','school','major','edu_background','grade',"hukou",'edu_fa','edu_ma','income')
        # finaldta[, factor_vars] <- lapply(finaldta[, factor_vars], as.factor)
        factor_vars = ['gender','location','university','school','major','edu_background','grade','hukou','edu_fa','edu_ma','income']
        for var in factor_vars:
            if var in self.final_data.columns:
                self.final_data[var] = self.final_data[var].astype('category')

        # 删除缺失值
        initial_rows = len(self.final_data)
        self.final_data = self.final_data.dropna()
        final_rows = len(self.final_data)

        print(f"数据清理: {initial_rows} -> {final_rows} 行 (删除了{initial_rows-final_rows}行缺失值)")

        return self.final_data

    def create_ordered_factors(self):
        """创建有序因子变量 - 精确复现R代码"""
        # R代码:
        # y1<-factor(finaldta[,'reject'],levels =1:5, labels = c('low','medium','high','very high','most high') )
        # y2<-factor(finaldta[,'parent'],levels =1:5, labels = c('low','medium','high','very high','most high') )
        # y3<-factor(finaldta[,'platform'],levels =1:5, labels = c('low','medium','high','very high','most high') )
        # y4<-factor(finaldta[,'law'],levels =1:5, labels = c('low','medium','high','very high','most high') )

        labels = ['low','medium','high','very high','most high']

        self.final_data['y1'] = pd.Categorical(self.final_data['reject'],
                                              categories=[1,2,3,4,5],
                                              ordered=True)
        self.final_data['y2'] = pd.Categorical(self.final_data['parent'],
                                              categories=[1,2,3,4,5],
                                              ordered=True)
        self.final_data['y3'] = pd.Categorical(self.final_data['platform'],
                                              categories=[1,2,3,4,5],
                                              ordered=True)
        self.final_data['y4'] = pd.Categorical(self.final_data['law'],
                                              categories=[1,2,3,4,5],
                                              ordered=True)

        return self.final_data

    def descriptive_statistics(self):
        """描述性统计 - 复现R代码的统计结果"""
        print("\n" + "="*80)
        print("描述性统计")
        print("="*80)

        # 网络成瘾和隐私感知的描述性统计
        print(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，"
              f"sd:{self.final_data['temp3'].std():.1f}，"
              f"最小值{self.final_data['temp3'].min()}，"
              f"最大值{self.final_data['temp3'].max()}")

        print(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，"
              f"sd:{self.final_data['pv'].std():.2f}，"
              f"最小值{self.final_data['pv'].min()}，"
              f"最大值{self.final_data['pv'].max()}")

        # 因变量频数统计
        dep_vars = ['reject', 'parent', 'platform', 'law']
        dep_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        for var, name in zip(dep_vars, dep_names):
            if var in self.final_data.columns:
                print(f"\n{name}频数统计:")
                freq = self.final_data[var].value_counts().sort_index()
                print(freq.to_string())

        # 人口统计学变量频数 - 复现R代码最后的统计
        total_n = len(self.final_data)
        print(f"\n人口统计学变量 (N={total_n}):")

        if 'gender' in self.final_data.columns:
            gender_freq = self.final_data['gender'].value_counts()
            print(f"性别分布: {dict(gender_freq/total_n)}")

        if 'location' in self.final_data.columns:
            location_freq = self.final_data['location'].value_counts()
            print(f"地区分布: {dict(location_freq/total_n)}")

        if 'grade' in self.final_data.columns:
            grade_freq = self.final_data['grade'].value_counts()
            print(f"年级分布: {dict(grade_freq/total_n)}")

        if 'hukou' in self.final_data.columns:
            hukou_freq = self.final_data['hukou'].value_counts()
            print(f"户口分布: {dict(hukou_freq/total_n)}")

        return self.final_data.describe()

    def run_ordered_probit_regression(self, dep_var, include_interaction=True, method='probit'):
        """运行有序Probit/Logit回归 - 精确复现R代码"""
        print(f"\n正在运行{dep_var}的有序{method}回归...")

        # 构建公式 - 精确复现R代码的公式
        if include_interaction:
            formula = f"{dep_var} ~ temp3 * pv + C(gender) + C(major) + C(grade) + C(location) + C(hukou) + C(edu_fa) + C(edu_ma) + C(income)"
        else:
            formula = f"{dep_var} ~ temp3 + pv + C(gender) + C(major) + C(grade) + C(location) + C(hukou) + C(edu_fa) + C(edu_ma) + C(income)"

        print(f"回归公式: {formula}")

        try:
            # 使用OrderedModel进行有序回归
            if method == 'probit':
                model = OrderedModel.from_formula(formula, data=self.final_data, distr='probit')
            else:  # logistic
                model = OrderedModel.from_formula(formula, data=self.final_data, distr='logit')

            result = model.fit(method='bfgs', maxiter=1000, disp=0)

            # 计算稳健标准误 - 复现R的vcovCL(model, type = "HC1")
            robust_cov = cov_hc1(result)
            robust_se = np.sqrt(np.diag(robust_cov))

            # 计算稳健t统计量和p值
            robust_tvalues = result.params / robust_se
            robust_pvalues = 2 * (1 - stats.norm.cdf(np.abs(robust_tvalues)))

            # 计算Pseudo R-squared - 复现R的McFadden R-square
            # R代码: 1 - (logLik(model)/logLik(null_model))
            null_formula = f"{dep_var} ~ 1"
            if method == 'probit':
                null_model = OrderedModel.from_formula(null_formula, data=self.final_data, distr='probit')
            else:
                null_model = OrderedModel.from_formula(null_formula, data=self.final_data, distr='logit')

            null_result = null_model.fit(method='bfgs', maxiter=1000, disp=0)
            pseudo_r2 = 1 - (result.llf / null_result.llf)

            # 卡方检验 - 复现R的anova(null_model, model)
            lr_stat = 2 * (result.llf - null_result.llf)
            df_diff = len(result.params) - len(null_result.params)
            lr_pvalue = 1 - chi2.cdf(lr_stat, df_diff)

            return {
                'model': result,
                'null_model': null_result,
                'pseudo_r2': pseudo_r2,
                'lr_stat': lr_stat,
                'lr_pvalue': lr_pvalue,
                'df_diff': df_diff,
                'coefficients': result.params,
                'std_errors': result.bse,
                'robust_std_errors': robust_se,
                'pvalues': result.pvalues,
                'robust_pvalues': robust_pvalues,
                'robust_tvalues': robust_tvalues,
                'formula': formula
            }

        except Exception as e:
            print(f"有序{method}回归失败: {e}")
            return None

    def run_ols_regression(self, dep_var, include_interaction=True):
        """运行OLS回归 - 精确复现R代码"""
        print(f"\n正在运行{dep_var}的OLS回归...")

        # 构建公式
        if include_interaction:
            formula = f"{dep_var} ~ temp3 * pv + C(gender) + C(major) + C(grade) + C(location) + C(hukou) + C(edu_fa) + C(edu_ma) + C(income)"
        else:
            formula = f"{dep_var} ~ temp3 + pv + C(gender) + C(major) + C(grade) + C(location) + C(hukou) + C(edu_fa) + C(edu_ma) + C(income)"

        print(f"OLS公式: {formula}")

        try:
            # 运行OLS回归
            model = sm.formula.ols(formula, data=self.final_data)
            result = model.fit()

            # 计算稳健标准误 - 复现R的vcovHC(model, type = "HC1")
            robust_result = result.get_robustcov_results(cov_type='HC1')

            return {
                'model': result,
                'robust_model': robust_result,
                'r_squared': result.rsquared,
                'adj_r_squared': result.rsquared_adj,
                'coefficients': result.params,
                'std_errors': result.bse,
                'robust_std_errors': robust_result.bse,
                'pvalues': result.pvalues,
                'robust_pvalues': robust_result.pvalues,
                'formula': formula
            }

        except Exception as e:
            print(f"OLS回归失败: {e}")
            return None

    def format_regression_results(self, result_dict, model_name, round_digits=3):
        """格式化回归结果 - 复现R的round(coeftest(), 3)输出格式"""
        if result_dict is None:
            print(f"{model_name}: 回归失败")
            return

        print(f"\n{'='*80}")
        print(f"{model_name}")
        print('='*80)

        # 使用稳健标准误的结果
        if 'robust_std_errors' in result_dict:
            coeffs = result_dict['coefficients']
            std_errs = result_dict['robust_std_errors']
            pvals = result_dict['robust_pvalues']
            if 'robust_tvalues' in result_dict:
                tvals = result_dict['robust_tvalues']
            else:
                tvals = coeffs / std_errs
        else:
            coeffs = result_dict['coefficients']
            std_errs = result_dict['std_errors']
            pvals = result_dict['pvalues']
            tvals = coeffs / std_errs

        # 格式化输出 - 复现R的输出格式
        print(f"{'变量':<25} {'系数':<12} {'标准误':<12} {'t值':<12} {'P值':<15} {'显著性'}")
        print('-' * 90)

        # 主要关注的变量
        key_vars = ['temp3', 'pv', 'temp3:pv']
        var_names = ['网络成瘾', '隐私感知', '网络成瘾:隐私感知']

        for var, name in zip(key_vars, var_names):
            if var in coeffs.index:
                coef = coeffs[var]
                se = std_errs[var] if var in std_errs.index else np.nan
                tval = tvals[var] if var in tvals.index else np.nan
                pval = pvals[var] if var in pvals.index else np.nan
                sig = self.get_significance_stars(pval)

                print(f"{name:<25} {coef:<12.{round_digits}f} {se:<12.{round_digits}f} {tval:<12.{round_digits}f} {pval:<15.3e} {sig}")

        # 显示其他重要系数（如果需要）
        print("\n其他控制变量:")
        for var in coeffs.index:
            if var not in key_vars and not var.startswith('C(') and var != 'Intercept':
                coef = coeffs[var]
                se = std_errs[var] if var in std_errs.index else np.nan
                tval = tvals[var] if var in tvals.index else np.nan
                pval = pvals[var] if var in pvals.index else np.nan
                sig = self.get_significance_stars(pval)

                print(f"{var:<25} {coef:<12.{round_digits}f} {se:<12.{round_digits}f} {tval:<12.{round_digits}f} {pval:<15.3e} {sig}")

        # 模型拟合指标
        if 'pseudo_r2' in result_dict:
            print(f"\nPseudo R-squared: {result_dict['pseudo_r2']:.6f}")
            if 'lr_stat' in result_dict:
                print(f"LR统计量: {result_dict['lr_stat']:.3f}")
                print(f"LR p值: {result_dict['lr_pvalue']:.3e}")
        elif 'adj_r_squared' in result_dict:
            print(f"\nAdjusted R-squared: {result_dict['adj_r_squared']:.6f}")
            print(f"R-squared: {result_dict['r_squared']:.6f}")

        return result_dict

    def get_significance_stars(self, pval):
        """获取显著性星号 - 复现R的显著性标记"""
        if pd.isna(pval):
            return ""
        elif pval < 0.001:
            return "***"
        elif pval < 0.01:
            return "**"
        elif pval < 0.05:
            return "*"
        elif pval < 0.1:
            return "."
        else:
            return ""

    def run_complete_analysis(self):
        """运行完整分析 - 精确复现R代码的所有分析步骤"""
        print("开始隐私保护行为分析 - 完全复现R语言结果...")
        print("="*100)

        # 1. 加载和准备数据
        self.load_data()
        self.prepare_final_dataset()
        self.create_ordered_factors()

        # 2. 描述性统计
        desc_stats = self.descriptive_statistics()

        # 3. 主要回归分析 - 复现R代码的四个因变量分析
        dependent_vars = ['y1', 'y2', 'y3', 'y4']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        self.results = {}

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            print(f"\n{'='*100}")
            print(f"分析因变量: {dep_name} ({dep_var})")
            print('='*100)

            # 有序Probit回归 (含交互项) - 复现R的主要模型
            probit_with_int = self.run_ordered_probit_regression(dep_var, include_interaction=True, method='probit')
            self.format_regression_results(probit_with_int, f"{dep_name} - 有序Probit回归 (含交互项)")

            # 有序Probit回归 (无交互项) - 复现R的对比模型
            probit_without_int = self.run_ordered_probit_regression(dep_var, include_interaction=False, method='probit')
            self.format_regression_results(probit_without_int, f"{dep_name} - 有序Probit回归 (无交互项)")

            # OLS回归 (稳健性检验) - 复现R的OLS分析
            ols_result = self.run_ols_regression(dep_var.replace('y', 'reject' if dep_var == 'y1' else
                                                                'parent' if dep_var == 'y2' else
                                                                'platform' if dep_var == 'y3' else 'law'),
                                               include_interaction=True)
            self.format_regression_results(ols_result, f"{dep_name} - OLS回归 (稳健性检验)")

            # 有序Logit回归 - 复现R的Logit分析
            logit_result = self.run_ordered_probit_regression(dep_var, include_interaction=True, method='logit')
            self.format_regression_results(logit_result, f"{dep_name} - 有序Logit回归")

            # 保存结果
            self.results[dep_var] = {
                'probit_with_interaction': probit_with_int,
                'probit_without_interaction': probit_without_int,
                'ols_robust': ols_result,
                'logit_with_interaction': logit_result
            }

        return self.results

    def save_complete_results(self, filename='complete_privacy_analysis_results.txt'):
        """保存完整结果到文件 - 复现PDF中的所有结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("隐私保护行为分析完整结果\n")
            f.write("Internet Addiction and Privacy Protection Analysis - Complete Results\n")
            f.write("="*100 + "\n\n")

            # 数据概况
            f.write("数据概况\n")
            f.write("-"*50 + "\n")
            f.write(f"总样本数: {len(self.final_data)}\n")
            f.write(f"变量数: {len(self.final_data.columns)}\n\n")

            # 描述性统计
            f.write("描述性统计\n")
            f.write("-"*50 + "\n")
            f.write(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，")
            f.write(f"sd:{self.final_data['temp3'].std():.1f}，")
            f.write(f"最小值{self.final_data['temp3'].min()}，")
            f.write(f"最大值{self.final_data['temp3'].max()}\n")

            f.write(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，")
            f.write(f"sd:{self.final_data['pv'].std():.2f}，")
            f.write(f"最小值{self.final_data['pv'].min()}，")
            f.write(f"最大值{self.final_data['pv'].max()}\n\n")

            # 因变量频数统计
            dependent_vars = ['y1', 'y2', 'y3', 'y4']
            dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']
            original_vars = ['reject', 'parent', 'platform', 'law']

            for i, (dep_var, dep_name, orig_var) in enumerate(zip(dependent_vars, dep_var_names, original_vars)):
                f.write(f"{i+1}. {dep_name}频数统计:\n")
                if orig_var in self.final_data.columns:
                    freq = self.final_data[orig_var].value_counts().sort_index()
                    for idx, count in freq.items():
                        f.write(f"  {idx}: {count}\n")
                f.write("\n")

            # 主要回归结果
            f.write("主要回归结果\n")
            f.write("="*100 + "\n\n")

            for i, (dep_var, dep_name) in enumerate(zip(dependent_vars, dep_var_names)):
                if dep_var in self.results:
                    f.write(f"{i+1}. {dep_name} ({dep_var})\n")
                    f.write("-"*80 + "\n")

                    # 有序Probit回归结果 (含交互项)
                    result = self.results[dep_var]['probit_with_interaction']
                    if result:
                        f.write("有序Probit回归 (含交互项):\n")

                        coeffs = result['coefficients']
                        std_errs = result['robust_std_errors']
                        pvals = result['robust_pvalues']

                        f.write(f"{'变量':<25} {'系数':<12} {'标准误':<12} {'P值':<15} {'显著性'}\n")
                        f.write("-" * 80 + "\n")

                        key_vars = ['temp3', 'pv', 'temp3:pv']
                        var_names = ['网络成瘾', '隐私感知', '网络成瘾:隐私感知']

                        for var, name in zip(key_vars, var_names):
                            if var in coeffs.index:
                                coef = coeffs[var]
                                se = std_errs[var] if var in std_errs.index else np.nan
                                pval = pvals[var] if var in pvals.index else np.nan
                                sig = self.get_significance_stars(pval)
                                f.write(f"{name:<25} {coef:<12.3f} {se:<12.3f} {pval:<15.3e} {sig}\n")

                        f.write(f"\nPseudo R-squared: {result['pseudo_r2']:.6f}\n")
                        f.write(f"LR统计量: {result['lr_stat']:.3f}\n")
                        f.write(f"LR p值: {result['lr_pvalue']:.3e}\n\n")

                    f.write("="*100 + "\n\n")

        print(f"\n完整结果已保存到文件: {filename}")

    def create_summary_table(self):
        """创建汇总表 - 复现PDF中的汇总结果表格"""
        print("\n" + "="*120)
        print("汇总结果表格")
        print("="*120)

        # 创建汇总表
        summary_data = []

        dependent_vars = ['y1', 'y2', 'y3', 'y4']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            if dep_var in self.results:
                result = self.results[dep_var]['probit_with_interaction']
                if result:
                    coeffs = result['coefficients']
                    std_errs = result['robust_std_errors']
                    pvals = result['robust_pvalues']

                    # 提取关键系数
                    temp3_coef = coeffs.get('temp3', np.nan)
                    temp3_se = std_errs.get('temp3', np.nan) if 'temp3' in std_errs.index else np.nan
                    temp3_pval = pvals.get('temp3', np.nan) if 'temp3' in pvals.index else np.nan

                    pv_coef = coeffs.get('pv', np.nan)
                    pv_se = std_errs.get('pv', np.nan) if 'pv' in std_errs.index else np.nan
                    pv_pval = pvals.get('pv', np.nan) if 'pv' in pvals.index else np.nan

                    interaction_coef = coeffs.get('temp3:pv', np.nan)
                    interaction_se = std_errs.get('temp3:pv', np.nan) if 'temp3:pv' in std_errs.index else np.nan
                    interaction_pval = pvals.get('temp3:pv', np.nan) if 'temp3:pv' in pvals.index else np.nan

                    summary_data.append({
                        '因变量': dep_name,
                        '网络成瘾系数': f"{temp3_coef:.3f}{self.get_significance_stars(temp3_pval)}",
                        '网络成瘾标准误': f"({temp3_se:.3f})",
                        '隐私感知系数': f"{pv_coef:.3f}{self.get_significance_stars(pv_pval)}",
                        '隐私感知标准误': f"({pv_se:.3f})",
                        '交互项系数': f"{interaction_coef:.3f}{self.get_significance_stars(interaction_pval)}",
                        '交互项标准误': f"({interaction_se:.3f})",
                        'Pseudo R²': f"{result['pseudo_r2']:.6f}"
                    })

        # 创建DataFrame并显示
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))

        # 保存汇总表
        summary_df.to_csv('privacy_analysis_summary_table.csv', index=False, encoding='utf-8-sig')
        print(f"\n汇总表已保存为: privacy_analysis_summary_table.csv")

        return summary_df


def main():
    """主函数 - 运行完整分析并生成所有结果文件"""
    # 数据文件路径
    data_path = '全国大学生网络素养数据.xlsx'

    try:
        print("隐私保护行为分析 - Python完全复现R语言结果")
        print("Internet Addiction and Privacy Protection Analysis")
        print("="*100)

        # 创建分析实例
        analysis = CompletePrivacyAnalysis(data_path)

        # 运行完整分析
        print("开始运行完整分析...")
        results = analysis.run_complete_analysis()

        # 创建汇总表
        print("\n创建汇总表...")
        summary_table = analysis.create_summary_table()

        # 保存完整结果
        print("\n保存完整结果...")
        analysis.save_complete_results('complete_privacy_analysis_results.txt')

        print("\n" + "="*100)
        print("分析完成！生成的文件:")
        print("- complete_privacy_analysis_results.txt (详细分析结果)")
        print("- privacy_analysis_summary_table.csv (汇总表)")
        print("="*100)

        # 显示关键发现
        print("\n关键发现摘要:")
        print("-"*50)

        for dep_var, dep_name in zip(['y1', 'y2', 'y3', 'y4'], ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']):
            if dep_var in results and results[dep_var]['probit_with_interaction']:
                result = results[dep_var]['probit_with_interaction']
                coeffs = result['coefficients']
                pvals = result['robust_pvalues']

                temp3_coef = coeffs.get('temp3', np.nan)
                temp3_pval = pvals.get('temp3', np.nan) if 'temp3' in pvals.index else np.nan

                pv_coef = coeffs.get('pv', np.nan)
                pv_pval = pvals.get('pv', np.nan) if 'pv' in pvals.index else np.nan

                interaction_coef = coeffs.get('temp3:pv', np.nan)
                interaction_pval = pvals.get('temp3:pv', np.nan) if 'temp3:pv' in pvals.index else np.nan

                print(f"\n{dep_name}:")
                print(f"  网络成瘾: {temp3_coef:.3f} (p={temp3_pval:.3e})")
                print(f"  隐私感知: {pv_coef:.3f} (p={pv_pval:.3e})")
                print(f"  交互效应: {interaction_coef:.3f} (p={interaction_pval:.3e})")
                print(f"  Pseudo R²: {result['pseudo_r2']:.6f}")

        return analysis, results

    except FileNotFoundError:
        print(f"错误：找不到数据文件 {data_path}")
        print("请确保数据文件在当前目录下")
        return None, None
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def install_required_packages():
    """安装所需的Python包"""
    required_packages = [
        'pandas',
        'numpy',
        'matplotlib',
        'seaborn',
        'scipy',
        'statsmodels',
        'openpyxl'
    ]

    import subprocess
    import sys

    print("检查并安装所需的Python包...")
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装完成")


if __name__ == "__main__":
    print("隐私保护行为分析 - Python完全复现版本")
    print("基于R语言代码的精确复现")
    print("="*100)

    # 可选：安装所需包
    # install_required_packages()

    # 运行主分析
    analysis, results = main()

    if analysis is not None:
        print("\n🎉 分析成功完成！")
        print("所有结果文件已生成，可以与原始R语言结果进行对比。")

        # 提供进一步分析的建议
        print("\n进一步分析建议:")
        print("1. 查看 complete_privacy_analysis_results.txt 了解详细结果")
        print("2. 查看 privacy_analysis_summary_table.csv 了解汇总表")
        print("3. 对比Python结果与原始PDF结果的一致性")
        print("4. 如需要，可以进一步进行异质性分析或稳健性检验")
    else:
        print("\n❌ 分析失败，请检查错误信息并重试。")
