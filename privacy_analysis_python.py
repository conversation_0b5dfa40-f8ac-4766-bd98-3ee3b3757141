#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python复现R语言隐私保护行为分析
Internet Addiction and Privacy Protection Analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
import statsmodels.api as sm
from statsmodels.miscmodels.ordinal_model import OrderedModel
from statsmodels.stats.outliers_influence import variance_inflation_factor
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class PrivacyAnalysis:
    def __init__(self, data_path):
        """初始化分析类"""
        self.data_path = data_path
        self.data = None
        self.final_data = None
        self.results = {}

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.data = pd.read_excel(self.data_path)
        print(f"数据形状: {self.data.shape}")
        return self.data

    def create_network_addiction_score(self):
        """创建网络成瘾得分 - 复现R代码中的temp3变量"""
        # R代码: temp3<-apply(data[,c(75,106,86,81,81,80,79,80,92,86,87,91,73,76,88,89,90,93,81,91)], 1, sum)
        # 注意：Python索引从0开始，R从1开始，所以需要减1
        addiction_cols = [74, 105, 85, 80, 80, 79, 78, 79, 91, 85, 86, 90, 72, 75, 87, 88, 89, 92, 80, 90]

        # 检查列是否存在
        valid_cols = [col for col in addiction_cols if col < self.data.shape[1]]

        if len(valid_cols) != len(addiction_cols):
            print(f"警告: 部分网络成瘾列不存在，使用可用的{len(valid_cols)}列")

        # 计算网络成瘾总分
        addiction_score = self.data.iloc[:, valid_cols].sum(axis=1)

        print(f"网络成瘾得分统计:")
        print(f"均值: {addiction_score.mean():.2f}")
        print(f"标准差: {addiction_score.std():.2f}")
        print(f"最小值: {addiction_score.min()}")
        print(f"最大值: {addiction_score.max()}")

        return addiction_score

    def create_privacy_perception_score(self):
        """创建隐私感知得分 - 复现R代码中的pv变量"""
        # R代码: pv<-apply(data[,c(147,149,150)], 1, mean)
        # Python索引: 146, 148, 149
        privacy_cols = [146, 148, 149]

        # 检查列是否存在
        valid_cols = [col for col in privacy_cols if col < self.data.shape[1]]

        if len(valid_cols) != len(privacy_cols):
            print(f"警告: 部分隐私感知列不存在，使用可用的{len(valid_cols)}列")

        # 计算隐私感知均值
        privacy_score = self.data.iloc[:, valid_cols].mean(axis=1)

        print(f"隐私感知得分统计:")
        print(f"均值: {privacy_score.mean():.2f}")
        print(f"标准差: {privacy_score.std():.2f}")
        print(f"最小值: {privacy_score.min()}")
        print(f"最大值: {privacy_score.max()}")

        return privacy_score

    def create_control_variables(self):
        """创建控制变量"""
        # R代码: geography<-data[,c(7:14,43:45)]
        # Python索引: 6:14, 42:45
        control_cols = list(range(6, 14)) + list(range(42, 45))

        # 检查列是否存在
        valid_cols = [col for col in control_cols if col < self.data.shape[1]]

        control_vars = self.data.iloc[:, valid_cols].copy()

        # 设置列名（对应R代码中的命名）
        expected_names = ['gender', 'location', 'university', 'school', 'major',
                         'edu_background', 'grade', 'hukou', 'edu_fa', 'edu_ma', 'income']

        # 根据实际可用列数调整列名
        actual_names = expected_names[:len(valid_cols)]
        control_vars.columns = actual_names

        return control_vars

    def create_dependent_variables(self):
        """创建因变量"""
        # R代码: data[,c(161:164)] -> reject, platform, parent, law
        # Python索引: 160:164
        dep_cols = list(range(160, 164))

        # 检查列是否存在
        valid_cols = [col for col in dep_cols if col < self.data.shape[1]]

        if len(valid_cols) < 4:
            print(f"警告: 只找到{len(valid_cols)}个因变量列")

        dep_vars = self.data.iloc[:, valid_cols].copy()

        # 设置列名
        dep_names = ['reject', 'platform', 'parent', 'law'][:len(valid_cols)]
        dep_vars.columns = dep_names

        return dep_vars

    def prepare_final_dataset(self):
        """准备最终分析数据集"""
        print("正在准备分析数据集...")

        # 创建各种变量
        addiction_score = self.create_network_addiction_score()
        privacy_score = self.create_privacy_perception_score()
        control_vars = self.create_control_variables()
        dep_vars = self.create_dependent_variables()

        # 合并数据
        self.final_data = pd.concat([
            control_vars,
            pd.Series(addiction_score, name='temp3'),
            pd.Series(privacy_score, name='pv'),
            dep_vars
        ], axis=1)

        # 删除缺失值
        initial_rows = len(self.final_data)
        self.final_data = self.final_data.dropna()
        final_rows = len(self.final_data)

        print(f"数据清理: {initial_rows} -> {final_rows} 行 (删除了{initial_rows-final_rows}行缺失值)")

        return self.final_data

    def descriptive_statistics(self):
        """描述性统计"""
        print("\n" + "="*50)
        print("描述性统计")
        print("="*50)

        # 网络成瘾和隐私感知的描述性统计
        print(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，"
              f"sd:{self.final_data['temp3'].std():.1f}，"
              f"最小值{self.final_data['temp3'].min()}，"
              f"最大值{self.final_data['temp3'].max()}")

        print(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，"
              f"sd:{self.final_data['pv'].std():.2f}，"
              f"最小值{self.final_data['pv'].min()}，"
              f"最大值{self.final_data['pv'].max()}")

        # 因变量频数统计
        dep_vars = ['reject', 'platform', 'parent', 'law']
        dep_names = ['拒绝使用', '平台反馈', '亲友求助', '诉诸法律']

        for var, name in zip(dep_vars, dep_names):
            if var in self.final_data.columns:
                print(f"\n{name}频数统计:")
                freq = self.final_data[var].value_counts().sort_index()
                print(freq.to_string())

        return self.final_data.describe()

    def run_ordered_probit_regression(self, dependent_var, include_interaction=True):
        """运行有序Probit回归"""
        print(f"\n正在运行{dependent_var}的有序Probit回归...")

        # 准备自变量
        X_vars = ['temp3', 'pv']

        # 添加控制变量（如果存在）
        control_vars = ['gender', 'major', 'grade', 'location', 'hukou', 'edu_fa', 'edu_ma', 'income']
        for var in control_vars:
            if var in self.final_data.columns:
                X_vars.append(var)

        # 创建设计矩阵
        X = self.final_data[X_vars].copy()

        # 处理分类变量
        categorical_vars = ['gender', 'major', 'grade', 'location', 'hukou', 'edu_fa', 'edu_ma', 'income']
        for var in categorical_vars:
            if var in X.columns:
                X[var] = X[var].astype('category')

        # 创建虚拟变量
        X_encoded = pd.get_dummies(X, drop_first=True)

        # 添加交互项
        if include_interaction:
            X_encoded['temp3_pv_interaction'] = self.final_data['temp3'] * self.final_data['pv']

        # 因变量
        y = self.final_data[dependent_var]

        try:
            # 使用OrderedModel进行有序回归
            model = OrderedModel(y, X_encoded, distr='probit')
            result = model.fit(method='bfgs', maxiter=1000)

            # 计算Pseudo R-squared
            null_model = OrderedModel(y, np.ones((len(y), 1)), distr='probit')
            null_result = null_model.fit(method='bfgs', maxiter=1000, disp=0)

            pseudo_r2 = 1 - (result.llf / null_result.llf)

            return {
                'model': result,
                'pseudo_r2': pseudo_r2,
                'summary': result.summary(),
                'coefficients': result.params,
                'pvalues': result.pvalues,
                'std_errors': result.bse
            }

        except Exception as e:
            print(f"有序Probit回归失败: {e}")
            print("尝试使用OLS回归作为替代...")
            return self.run_ols_regression(dependent_var, include_interaction)

    def run_ols_regression(self, dependent_var, include_interaction=True):
        """运行OLS回归作为稳健性检验"""
        print(f"\n正在运行{dependent_var}的OLS回归...")

        # 准备自变量
        X_vars = ['temp3', 'pv']

        # 添加控制变量
        control_vars = ['gender', 'major', 'grade', 'location', 'hukou', 'edu_fa', 'edu_ma', 'income']
        for var in control_vars:
            if var in self.final_data.columns:
                X_vars.append(var)

        # 创建设计矩阵
        X = self.final_data[X_vars].copy()

        # 处理分类变量
        categorical_vars = ['gender', 'major', 'grade', 'location', 'hukou', 'edu_fa', 'edu_ma', 'income']
        for var in categorical_vars:
            if var in X.columns:
                X[var] = X[var].astype('category')

        # 创建虚拟变量
        X_encoded = pd.get_dummies(X, drop_first=True)

        # 添加交互项
        if include_interaction:
            X_encoded['temp3_pv_interaction'] = self.final_data['temp3'] * self.final_data['pv']

        # 添加常数项
        X_encoded = sm.add_constant(X_encoded)

        # 因变量
        y = self.final_data[dependent_var]

        # 运行回归
        model = sm.OLS(y, X_encoded)
        result = model.fit(cov_type='HC1')  # 使用稳健标准误

        return {
            'model': result,
            'r_squared': result.rsquared,
            'adj_r_squared': result.rsquared_adj,
            'summary': result.summary(),
            'coefficients': result.params,
            'pvalues': result.pvalues,
            'std_errors': result.bse
        }

    def format_regression_results(self, result_dict, dependent_var_name):
        """格式化回归结果"""
        print(f"\n{'='*60}")
        print(f"因变量：{dependent_var_name}")
        print('='*60)

        # 提取关键系数
        coeffs = result_dict['coefficients']
        pvals = result_dict['pvalues']
        std_errs = result_dict['std_errors']

        # 格式化输出
        print(f"{'变量':<20} {'系数':<10} {'标准误':<10} {'P值':<15}")
        print('-' * 60)

        # 主要变量
        key_vars = ['temp3', 'pv', 'temp3_pv_interaction']
        var_names = ['网络成瘾', '网络隐私感知', '网络成瘾:网络隐私感知']

        for var, name in zip(key_vars, var_names):
            if var in coeffs.index:
                coef = coeffs[var]
                se = std_errs[var]
                pval = pvals[var]
                sig = self.get_significance_stars(pval)
                print(f"{name:<20} {coef:<10.3f} {se:<10.3f} {pval:<10.3e} {sig}")

        # 模型拟合指标
        if 'pseudo_r2' in result_dict:
            print(f"\nPseudo R-squared: {result_dict['pseudo_r2']:.6f}")
        elif 'adj_r_squared' in result_dict:
            print(f"\nAdjusted R-squared: {result_dict['adj_r_squared']:.4f}")

        return result_dict

    def get_significance_stars(self, pval):
        """获取显著性星号"""
        if pval < 0.001:
            return "***"
        elif pval < 0.01:
            return "**"
        elif pval < 0.05:
            return "*"
        elif pval < 0.1:
            return "."
        else:
            return ""

    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始隐私保护行为分析...")
        print("="*80)

        # 1. 加载和准备数据
        self.load_data()
        self.prepare_final_dataset()

        # 2. 描述性统计
        desc_stats = self.descriptive_statistics()

        # 3. 回归分析
        dependent_vars = ['reject', 'parent', 'platform', 'law']
        dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

        self.results = {}

        for dep_var, dep_name in zip(dependent_vars, dep_var_names):
            if dep_var in self.final_data.columns:
                print(f"\n{'='*80}")
                print(f"分析因变量: {dep_name}")
                print('='*80)

                # 有交互项的模型
                result_with_interaction = self.run_ordered_probit_regression(dep_var, include_interaction=True)
                self.format_regression_results(result_with_interaction, f"{dep_name}（含交互项）")

                # 无交互项的模型
                result_without_interaction = self.run_ordered_probit_regression(dep_var, include_interaction=False)
                self.format_regression_results(result_without_interaction, f"{dep_name}（无交互项）")

                # OLS稳健性检验
                ols_result = self.run_ols_regression(dep_var, include_interaction=True)
                self.format_regression_results(ols_result, f"{dep_name}（OLS稳健性检验）")

                # 保存结果
                self.results[dep_var] = {
                    'with_interaction': result_with_interaction,
                    'without_interaction': result_without_interaction,
                    'ols_robust': ols_result
                }

        return self.results

    def save_results_to_file(self, filename='privacy_analysis_results.txt'):
        """保存结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("隐私保护行为分析结果\n")
            f.write("="*80 + "\n\n")

            # 描述性统计
            f.write("描述性统计\n")
            f.write("-"*40 + "\n")
            f.write(f"网络成瘾均值：{self.final_data['temp3'].mean():.2f}，")
            f.write(f"sd:{self.final_data['temp3'].std():.1f}，")
            f.write(f"最小值{self.final_data['temp3'].min()}，")
            f.write(f"最大值{self.final_data['temp3'].max()}\n")

            f.write(f"网络隐私感知均值：{self.final_data['pv'].mean():.2f}，")
            f.write(f"sd:{self.final_data['pv'].std():.2f}，")
            f.write(f"最小值{self.final_data['pv'].min()}，")
            f.write(f"最大值{self.final_data['pv'].max()}\n\n")

            # 回归结果
            dependent_vars = ['reject', 'parent', 'platform', 'law']
            dep_var_names = ['拒绝使用', '亲友求助', '平台反馈', '诉诸法律']

            for i, (dep_var, dep_name) in enumerate(zip(dependent_vars, dep_var_names)):
                if dep_var in self.results:
                    f.write(f"{i+1}. 因变量：{dep_name}\n")
                    f.write("-"*60 + "\n")

                    # 频数统计
                    if dep_var in self.final_data.columns:
                        freq = self.final_data[dep_var].value_counts().sort_index()
                        f.write("频数统计:\n")
                        for idx, count in freq.items():
                            f.write(f"  {idx}: {count}\n")

                    # 主要结果（含交互项）
                    result = self.results[dep_var]['with_interaction']
                    coeffs = result['coefficients']
                    pvals = result['pvalues']
                    std_errs = result['std_errors']

                    f.write("\n主要回归结果（含交互项）:\n")
                    f.write(f"{'变量':<20} {'系数':<10} {'标准误':<10} {'P值':<15}\n")
                    f.write("-" * 60 + "\n")

                    key_vars = ['temp3', 'pv', 'temp3_pv_interaction']
                    var_names = ['网络成瘾', '网络隐私感知', '网络成瘾:网络隐私感知']

                    for var, name in zip(key_vars, var_names):
                        if var in coeffs.index:
                            coef = coeffs[var]
                            se = std_errs[var]
                            pval = pvals[var]
                            sig = self.get_significance_stars(pval)
                            f.write(f"{name:<20} {coef:<10.3f} {se:<10.3f} {pval:<10.3e} {sig}\n")

                    # 模型拟合指标
                    if 'pseudo_r2' in result:
                        f.write(f"\nPseudo R-squared: {result['pseudo_r2']:.6f}\n")
                    elif 'adj_r_squared' in result:
                        f.write(f"\nAdjusted R-squared: {result['adj_r_squared']:.4f}\n")

                    f.write("\n" + "="*80 + "\n\n")

        print(f"\n结果已保存到文件: {filename}")

    def create_visualization(self):
        """创建可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('隐私保护行为分析可视化', fontsize=16)

        # 1. 网络成瘾和隐私感知的分布
        axes[0, 0].hist(self.final_data['temp3'], bins=30, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('网络成瘾得分分布')
        axes[0, 0].set_xlabel('网络成瘾得分')
        axes[0, 0].set_ylabel('频数')

        axes[0, 1].hist(self.final_data['pv'], bins=30, alpha=0.7, color='lightgreen')
        axes[0, 1].set_title('隐私感知得分分布')
        axes[0, 1].set_xlabel('隐私感知得分')
        axes[0, 1].set_ylabel('频数')

        # 2. 散点图：网络成瘾 vs 隐私感知
        axes[1, 0].scatter(self.final_data['temp3'], self.final_data['pv'], alpha=0.5)
        axes[1, 0].set_title('网络成瘾 vs 隐私感知')
        axes[1, 0].set_xlabel('网络成瘾得分')
        axes[1, 0].set_ylabel('隐私感知得分')

        # 3. 因变量分布（如果存在）
        if 'reject' in self.final_data.columns:
            reject_counts = self.final_data['reject'].value_counts().sort_index()
            axes[1, 1].bar(reject_counts.index, reject_counts.values, alpha=0.7, color='coral')
            axes[1, 1].set_title('拒绝使用行为分布')
            axes[1, 1].set_xlabel('拒绝程度')
            axes[1, 1].set_ylabel('频数')

        plt.tight_layout()
        plt.savefig('privacy_analysis_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("可视化图表已保存为: privacy_analysis_visualization.png")


def main():
    """主函数 - 运行完整分析"""
    # 数据文件路径
    data_path = '全国大学生网络素养数据.xlsx'

    try:
        # 创建分析实例
        analysis = PrivacyAnalysis(data_path)

        # 运行完整分析
        results = analysis.run_complete_analysis()

        # 保存结果到文件
        analysis.save_results_to_file('privacy_analysis_results.txt')

        # 创建可视化
        analysis.create_visualization()

        print("\n" + "="*80)
        print("分析完成！")
        print("结果文件:")
        print("- privacy_analysis_results.txt (详细结果)")
        print("- privacy_analysis_visualization.png (可视化图表)")
        print("="*80)

        return analysis, results

    except FileNotFoundError:
        print(f"错误：找不到数据文件 {data_path}")
        print("请确保数据文件在当前目录下")
        return None, None
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def install_required_packages():
    """安装所需的Python包"""
    required_packages = [
        'pandas',
        'numpy',
        'matplotlib',
        'seaborn',
        'scipy',
        'scikit-learn',
        'statsmodels',
        'openpyxl'
    ]

    import subprocess
    import sys

    print("检查并安装所需的Python包...")
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装完成")


if __name__ == "__main__":
    print("隐私保护行为分析 - Python版本")
    print("复现R语言分析结果")
    print("="*80)

    # 安装所需包（可选）
    # install_required_packages()

    # 运行主分析
    analysis, results = main()

    if analysis is not None:
        print("\n分析成功完成！")
        print("你可以查看生成的结果文件了解详细分析结果。")
    else:
        print("\n分析失败，请检查错误信息。")
